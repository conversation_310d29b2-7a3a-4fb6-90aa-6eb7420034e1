"""Gunicorn configuration for production deployment.

Usage example (from project root):
   gunicorn -c backend/gunicorn_config.py backend.wsgi:app

These settings favour small‑footprint homelab hardware while retaining
reasonable concurrency and graceful restart behaviour.  Adjust `workers` or
`threads` if you have more CPU cores available.
"""

import multiprocessing

# ---------------------------------------------------------------------------
# Core settings
# ---------------------------------------------------------------------------

bind = "127.0.0.1:8000"  # Cloudflare Tunnel will expose this port externally
workers = max(multiprocessing.cpu_count(), 2)
threads = 2  # lightweight threads per worker for I/O wait
worker_class = "gthread"  # safe with <PERSON>lask (pure Python, IO‑bound)

# ---------------------------------------------------------------------------
# Reliability / restart tuning
# ---------------------------------------------------------------------------

max_requests = 1000  # recycle workers to avoid memory leaks
max_requests_jitter = 100
timeout = 30  # seconds
keepalive = 5

# ---------------------------------------------------------------------------
# Logging (delegated to systemd journal)
# ---------------------------------------------------------------------------

accesslog = "-"  # stdout
errorlog = "-"  # stderr
loglevel = "info"

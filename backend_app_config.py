from decouple import config as env


class Config:
    """Base configuration loaded from environment variables."""

    # --- Core settings -----------------------------------------------------
    SECRET_KEY: str = env("SECRET_KEY", default="change-me")
    MONGO_URI: str = env(
        "MONGO_URI",
        default="mongodb://localhost:27017/personal_organizer",
    )

    # Cookies & session security
    SESSION_COOKIE_SECURE: bool = True
    SESSION_COOKIE_HTTPONLY: bool = True
    SESSION_COOKIE_SAMESITE: str = "Lax"

    # Rate‑limiting (Flask‑Limiter syntax)
    RATELIMIT_DEFAULT: str = "5 per minute"
    RATELIMIT_KEY_FUNC: str = "flask_limiter.util.get_remote_address"

    # Weather integration
    WEATHER_CACHE_TTL: int = 7200  # seconds (2 hours)

    # Dashboard: empty default layout skeleton per breakpoint
    DEFAULT_DASHBOARD_LAYOUT = {
        "lg": [],
        "md": [],
        "sm": [],
        "xs": [],
        "xxs": [],
    }


class DevelopmentConfig(Config):
    """Config tweaks for local development."""

    DEBUG: bool = True
    SESSION_COOKIE_SECURE: bool = False  # allow HTTP on localhost


class ProductionConfig(Config):
    """Production‑grade settings (default)."""

    DEBUG: bool = False


# ---------------------------------------------------------------------------
# Helper to fetch the appropriate configuration class for the current env.
# ---------------------------------------------------------------------------
_config_map = {
    "development": DevelopmentConfig,
    "production": ProductionConfig,
    "default": ProductionConfig,
}


def get_config():
    """Return the appropriate Config subclass using FLASK_ENV."""

    env_name = env("FLASK_ENV", default="production")
    return _config_map.get(env_name, ProductionConfig)

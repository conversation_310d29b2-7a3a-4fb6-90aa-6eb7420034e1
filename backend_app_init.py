from __future__ import annotations

"""Flask application factory for the Personal Organizer API.

This module provides a single public function `create_app()` which builds and
configures an instance of :class:`flask.Flask` using environment‑driven config
classes and attaches all core extensions.  Blueprints and error handlers are
registered in separate modules to keep concerns isolated; importing them here
would create circular dependencies during initial scaffolding.
"""

from flask import Flask, jsonify
from werkzeug.exceptions import HTTPException

from .config import get_config
from .extensions import init_extensions


# ---------------------------------------------------------------------------
# Public factory
# ---------------------------------------------------------------------------


def create_app(config_class: type | None = None) -> Flask:
    """Application factory compliant with Gunicorn and Flask CLI.

    Parameters
    ----------
    config_class : type | None
        Optional subclass of :class:`Config` to override the default one that
        is inferred from the ``FLASK_ENV`` environment variable.

    Returns
    -------
    flask.Flask
        A fully initialised Flask application instance.
    """

    # Instantiate
    app = Flask(__name__, static_folder=None)

    # Configuration
    cfg_class = config_class or get_config()
    app.config.from_object(cfg_class)

    # Extensions
    init_extensions(app)

    # Error handling: convert Werkzeug HTTPException → JSON consistently
    @app.errorhandler(HTTPException)
    def _http_error(exc: HTTPException):  # pragma: no cover
        response = {
            "error": {
                "code": exc.code,
                "name": exc.name,
                "description": exc.description,
            }
        }
        return jsonify(response), exc.code

    # Healthcheck route (useful for systemd + Cloudflare Tunnel)
    @app.get("/healthz")
    def healthcheck():  # pragma: no cover
        return {"status": "ok"}, 200

    return app

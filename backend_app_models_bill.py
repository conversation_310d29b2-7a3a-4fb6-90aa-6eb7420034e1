from __future__ import annotations

"""Bill model helper wrapping the `bills` collection.

Supports one‑off and recurring entries.  Recurring rules follow RFC 5545 RRULE
syntax (e.g. "FREQ=MONTHLY;BYMONTHDAY=1" for rent on the 1st).  Helper
`upcoming_instances` expands at query‑time for the next *N* months without
materialising documents – front‑end receives dates already localised on render.
"""

from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional

from bson.objectid import ObjectId
from dateutil.rrule import rrulestr

from app.extensions import mongo

COLL = mongo.db.bills

# Expand 12 months into the future by default
_DEFAULT_HORIZON = 12  # months


class Bill:
    """Lightweight adapter around a single bill document."""

    def __init__(self, doc: Dict[str, Any]):
        self._doc = doc
        self.id: str = str(doc["_id"])

    # ---------------------------------------------------------------------
    # Creation helpers
    # ---------------------------------------------------------------------

    @classmethod
    def create(
        cls,
        *,
        user_id: str | ObjectId,
        title: str,
        amount: Optional[Decimal] = None,
        category: Optional[str] = None,
        notes: Optional[str] = None,
        due_date: Optional[datetime] = None,
        rrule: Optional[str] = None,
    ) -> "Bill":
        """Insert a new bill (one‑off *or* recurring)."""

        if not (due_date or rrule):
            raise ValueError("Either due_date or rrule must be provided")
        if due_date and rrule:
            raise ValueError("Provide only one of due_date or rrule, not both")

        now = datetime.utcnow().replace(tzinfo=timezone.utc)
        doc: Dict[str, Any] = {
            "user_id": ObjectId(user_id),
            "title": title,
            "amount": amount,
            "category": category,
            "notes": notes,
            "due_date": due_date,
            "rrule": rrule,
            "created_at": now,
            "updated_at": now,
        }
        COLL.insert_one(doc)
        return cls(doc)

    # ---------------------------------------------------------------------
    # Lookups
    # ---------------------------------------------------------------------

    @classmethod
    def get(cls, bill_id: str | ObjectId) -> "Bill | None":
        obj_id = ObjectId(bill_id) if isinstance(bill_id, str) else bill_id
        if (doc := COLL.find_one({"_id": obj_id})) is not None:
            return cls(doc)
        return None

    @classmethod
    def list_by_user(cls, user_id: str | ObjectId) -> List["Bill"]:
        cursor = COLL.find({"user_id": ObjectId(user_id)}).sort("title")
        return [cls(doc) for doc in cursor]

    # ---------------------------------------------------------------------
    # Instance operations
    # ---------------------------------------------------------------------

    def delete(self):
        COLL.delete_one({"_id": ObjectId(self.id)})

    def update(self, **fields):
        if not fields:
            return
        fields["updated_at"] = datetime.utcnow().replace(tzinfo=timezone.utc)
        COLL.update_one({"_id": ObjectId(self.id)}, {"$set": fields})
        self._doc.update(fields)

    # ---------------------------------------------------------------------
    # Recurrence expansion helper
    # ---------------------------------------------------------------------

    def upcoming_instances(
        self,
        *,
        horizon_months: int = _DEFAULT_HORIZON,
        from_dt: Optional[datetime] = None,
    ) -> List[datetime]:
        """Return due dates (UTC) within *horizon_months* from *from_dt*."""

        from_dt = from_dt or datetime.utcnow().replace(tzinfo=timezone.utc)
        to_dt = from_dt + timedelta(days=30 * horizon_months)

        # One‑off
        if self._doc.get("due_date"):
            due: datetime = self._doc["due_date"].replace(tzinfo=timezone.utc)
            return [due] if from_dt <= due <= to_dt else []

        # Recurring using RRULE
        rule_str: str = self._doc["rrule"]
        # Use DTSTART = created_at as baseline; keep in UTC for calculations
        dtstart: datetime = self._doc["created_at"].replace(tzinfo=timezone.utc)
        rule = rrulestr(rule_str, dtstart=dtstart)
        return list(rule.between(from_dt, to_dt, inc=True))

    # ---------------------------------------------------------------------
    # Serialisation
    # ---------------------------------------------------------------------

    def to_dict(self, *, include_instances: bool = False) -> Dict[str, Any]:
        data: Dict[str, Any] = {
            "id": self.id,
            "title": self._doc["title"],
            "amount": (str(self._doc["amount"]) if self._doc.get("amount") else None),
            "category": self._doc.get("category"),
            "notes": self._doc.get("notes"),
            "due_date": (self._doc["due_date"].isoformat() if self._doc.get("due_date") else None),
            "rrule": self._doc.get("rrule"),
        }
        if include_instances:
            data["instances"] = [d.isoformat() for d in self.upcoming_instances()]
        return data

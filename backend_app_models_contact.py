from __future__ import annotations

"""Contact model helper wrapping the `contacts` collection."""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from bson.objectid import ObjectId

from app.extensions import mongo
from app.models.contact_type import ContactType

COLL = mongo.db.contacts


class Contact:
    """Adapter around a contact document."""

    def __init__(self, doc: Dict[str, Any]):
        self._doc = doc
        self.id: str = str(doc["_id"])

    # ------------------------------------------------------------------
    # Creation & retrieval
    # ------------------------------------------------------------------

    @classmethod
    def create(
        cls,
        *,
        user_id: str | ObjectId,
        type_id: str | ObjectId,
        name: str,
        last_contact: Optional[datetime] = None,
    ) -> "Contact":
        last_contact = last_contact or datetime.utcnow().replace(tzinfo=timezone.utc)
        doc = {
            "user_id": ObjectId(user_id),
            "type_id": ObjectId(type_id),
            "name": name.strip(),
            "last_contact": last_contact,
        }
        COLL.insert_one(doc)
        return cls(doc)

    @classmethod
    def get(cls, contact_id: str | ObjectId) -> "Contact | None":
        obj_id = ObjectId(contact_id) if isinstance(contact_id, str) else contact_id
        if (doc := COLL.find_one({"_id": obj_id})) is not None:
            return cls(doc)
        return None

    @classmethod
    def list_by_user(cls, user_id: str | ObjectId) -> List["Contact"]:
        cursor = COLL.find({"user_id": ObjectId(user_id)}).sort("name")
        return [cls(d) for d in cursor]

    # ------------------------------------------------------------------
    # Instance mutators
    # ------------------------------------------------------------------

    def delete(self):
        COLL.delete_one({"_id": ObjectId(self.id)})

    def ping(self):
        """Update last_contact timestamp to now."""
        now = datetime.utcnow().replace(tzinfo=timezone.utc)
        COLL.update_one({"_id": ObjectId(self.id)}, {"$set": {"last_contact": now}})
        self._doc["last_contact"] = now

    def update_type(self, new_type_id: str | ObjectId):
        COLL.update_one({"_id": ObjectId(self.id)}, {"$set": {"type_id": ObjectId(new_type_id)}})
        self._doc["type_id"] = ObjectId(new_type_id)

    # ------------------------------------------------------------------
    # Status helper (green / yellow / red)
    # ------------------------------------------------------------------

    def _threshold_days(self) -> int:
        ct = ContactType.get(self._doc["type_id"])
        return ct._doc["threshold_days"] if ct else 7  # sensible fallback

    def status(self) -> str:
        """Return colour status string based on threshold_days."""
        thresh = self._threshold_days()
        days_since = (datetime.utcnow().replace(tzinfo=timezone.utc) - self._doc["last_contact"]).days
        if days_since <= thresh:
            return "green"
        if days_since <= 2 * thresh:
            return "yellow"
        return "red"

    # ------------------------------------------------------------------
    # Serialisation
    # ------------------------------------------------------------------

    def to_dict(self, *, include_status: bool = True) -> Dict[str, Any]:
        data = {
            "id": self.id,
            "name": self._doc["name"],
            "type_id": str(self._doc["type_id"]),
            "last_contact": self._doc["last_contact"].isoformat(),
        }
        if include_status:
            data["status"] = self.status()
        return data

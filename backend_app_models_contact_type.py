from __future__ import annotations

"""Contact‑Type model helper.

Each *contact type* (e.g. Friend, Family, Client) defines a colour‑coding
threshold in **days** used by the dashboard to highlight how recently a user
has spoken with a contact of that type.
"""

from typing import Any, Dict, List, Optional

from bson.objectid import ObjectId

from app.extensions import mongo

COLL = mongo.db.contact_types


class ContactType:
    """Thin adapter around the `contact_types` collection."""

    def __init__(self, doc: Dict[str, Any]):
        self._doc = doc
        self.id: str = str(doc["_id"])

    # ------------------------------------------------------------------
    # CRUD helpers
    # ------------------------------------------------------------------

    @classmethod
    def create(cls, *, label: str, threshold_days: int) -> "ContactType":
        doc = {
            "label": label.strip(),
            "threshold_days": int(threshold_days),
        }
        result = COLL.insert_one(doc)
        doc["_id"] = result.inserted_id
        return cls(doc)

    @classmethod
    def get(cls, ct_id: str | ObjectId) -> "ContactType | None":
        obj_id = ObjectId(ct_id) if isinstance(ct_id, str) else ct_id
        if (doc := COLL.find_one({"_id": obj_id})) is not None:
            return cls(doc)
        return None

    @classmethod
    def by_label(cls, label: str) -> "ContactType | None":
        if (doc := COLL.find_one({"label": label.strip()})) is not None:
            return cls(doc)
        return None

    @classmethod
    def list_all(cls) -> List["ContactType"]:
        return [cls(d) for d in COLL.find().sort("label")]

    # ------------------------------------------------------------------
    # Instance mutators
    # ------------------------------------------------------------------

    def update(self, *, label: Optional[str] = None, threshold_days: Optional[int] = None) -> None:
        update: Dict[str, Any] = {}
        if label is not None:
            update["label"] = label.strip()
        if threshold_days is not None:
            update["threshold_days"] = int(threshold_days)
        if update:
            COLL.update_one({"_id": ObjectId(self.id)}, {"$set": update})
            self._doc.update(update)

    def delete(self) -> None:
        COLL.delete_one({"_id": ObjectId(self.id)})

    # ------------------------------------------------------------------
    # Serialisation
    # ------------------------------------------------------------------

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "label": self._doc["label"],
            "threshold_days": self._doc["threshold_days"],
        }

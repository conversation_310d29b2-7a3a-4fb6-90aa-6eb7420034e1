from __future__ import annotations

"""Event / Appointment model helper wrapping `events` collection.

Similar to bills but with `start` and `end` datetimes (both UTC). Recurrence
handled via RFC 5545 RRULE; upcoming instances expand within a configurable
horizon (12 months default).
"""

from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional

from bson.objectid import ObjectId
from dateutil.rrule import rrulestr

from app.extensions import mongo

COLL = mongo.db.events
_DEFAULT_HORIZON = 12  # months


class Event:
    """Lightweight adapter around an event document."""

    def __init__(self, doc: Dict[str, Any]):
        self._doc = doc
        self.id: str = str(doc["_id"])

    # ---------------------------------------------------------------------
    # Creation
    # ---------------------------------------------------------------------

    @classmethod
    def create(
        cls,
        *,
        user_id: str | ObjectId,
        title: str,
        start: datetime,
        end: datetime,
        notes: Optional[str] = None,
        rrule: Optional[str] = None,
    ) -> "Event":
        if start.tzinfo is None or end.tzinfo is None:
            raise ValueError("start/end must be timezone-aware")
        if end <= start:
            raise ValueError("end must be after start")

        now = datetime.utcnow().replace(tzinfo=timezone.utc)
        doc: Dict[str, Any] = {
            "user_id": ObjectId(user_id),
            "title": title,
            "start": start,
            "end": end,
            "rrule": rrule,
            "notes": notes,
            "created_at": now,
            "updated_at": now,
        }
        COLL.insert_one(doc)
        return cls(doc)

    # ---------------------------------------------------------------------
    # Lookups
    # ---------------------------------------------------------------------

    @classmethod
    def get(cls, event_id: str | ObjectId) -> "Event | None":
        obj_id = ObjectId(event_id) if isinstance(event_id, str) else event_id
        if (doc := COLL.find_one({"_id": obj_id})) is not None:
            return cls(doc)
        return None

    @classmethod
    def list_by_user(cls, user_id: str | ObjectId) -> List["Event"]:
        cursor = COLL.find({"user_id": ObjectId(user_id)}).sort("start")
        return [cls(doc) for doc in cursor]

    # ---------------------------------------------------------------------
    # Instance operations
    # ---------------------------------------------------------------------

    def delete(self):
        COLL.delete_one({"_id": ObjectId(self.id)})

    def update(self, **fields):
        if not fields:
            return
        fields["updated_at"] = datetime.utcnow().replace(tzinfo=timezone.utc)
        COLL.update_one({"_id": ObjectId(self.id)}, {"$set": fields})
        self._doc.update(fields)

    # ---------------------------------------------------------------------
    # Recurrence expansion
    # ---------------------------------------------------------------------

    def upcoming_instances(
        self,
        *,
        horizon_months: int = _DEFAULT_HORIZON,
        from_dt: Optional[datetime] = None,
    ) -> List[Dict[str, datetime]]:
        """Return list of {'start', 'end'} dicts within horizon."""

        from_dt = from_dt or datetime.utcnow().replace(tzinfo=timezone.utc)
        to_dt = from_dt + timedelta(days=30 * horizon_months)

        if self._doc.get("rrule") is None:
            start = self._doc["start"]
            end = self._doc["end"]
            return [{"start": start, "end": end}] if from_dt <= end <= to_dt else []

        rule_str = self._doc["rrule"]
        dtstart = self._doc["start"]  # first occurrence baseline
        rule = rrulestr(rule_str, dtstart=dtstart)
        occurrences = rule.between(from_dt, to_dt, inc=True)
        duration = self._doc["end"] - self._doc["start"]
        return [{"start": occ, "end": occ + duration} for occ in occurrences]

    # ---------------------------------------------------------------------
    # Serialisation
    # ---------------------------------------------------------------------

    def to_dict(self, *, include_instances: bool = False) -> Dict[str, Any]:
        data: Dict[str, Any] = {
            "id": self.id,
            "title": self._doc["title"],
            "start": self._doc["start"].isoformat(),
            "end": self._doc["end"].isoformat(),
            "rrule": self._doc.get("rrule"),
            "notes": self._doc.get("notes"),
        }
        if include_instances:
            data["instances"] = [
                {"start": i["start"].isoformat(), "end": i["end"].isoformat()} for i in self.upcoming_instances()
            ]
        return data

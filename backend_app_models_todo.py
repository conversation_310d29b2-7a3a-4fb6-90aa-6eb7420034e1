from __future__ import annotations

"""To‑Do model helper wrapping the `todos` collection.

Each item is a simple text entry with `created_at` and optional
`completed_at`.  Once marked complete the front‑end will remove it immediately;
however, the document remains until the TTL index (24 h) purges it.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from bson.objectid import ObjectId

from app.extensions import mongo

COLL = mongo.db.todos


class Todo:
    """Lightweight adapter around a to‑do document."""

    def __init__(self, doc: Dict[str, Any]):
        self._doc = doc
        self.id: str = str(doc["_id"])

    # ------------------------------------------------------------------
    # Creation & retrieval
    # ------------------------------------------------------------------

    @classmethod
    def create(cls, *, user_id: str | ObjectId, text: str) -> "Todo":
        now = datetime.utcnow().replace(tzinfo=timezone.utc)
        doc = {
            "user_id": ObjectId(user_id),
            "text": text,
            "created_at": now,
            "completed_at": None,
        }
        COLL.insert_one(doc)
        return cls(doc)

    @classmethod
    def get(cls, todo_id: str | ObjectId) -> "Todo | None":
        obj_id = ObjectId(todo_id) if isinstance(todo_id, str) else todo_id
        if (doc := COLL.find_one({"_id": obj_id})) is not None:
            return cls(doc)
        return None

    @classmethod
    def list_by_user(cls, user_id: str | ObjectId, *, include_completed: bool = False) -> List["Todo"]:
        query: Dict[str, Any] = {"user_id": ObjectId(user_id)}
        if not include_completed:
            query["completed_at"] = None
        cursor = COLL.find(query).sort("created_at", -1)
        return [cls(doc) for doc in cursor]

    # ------------------------------------------------------------------
    # Instance mutators
    # ------------------------------------------------------------------

    def delete(self):
        COLL.delete_one({"_id": ObjectId(self.id)})

    def mark_complete(self):
        now = datetime.utcnow().replace(tzinfo=timezone.utc)
        COLL.update_one({"_id": ObjectId(self.id)}, {"$set": {"completed_at": now}})
        self._doc["completed_at"] = now

    # ------------------------------------------------------------------
    # Serialisation
    # ------------------------------------------------------------------

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "text": self._doc["text"],
            "created_at": self._doc["created_at"].isoformat(),
            "completed_at": (
                self._doc["completed_at"].isoformat() if self._doc.get("completed_at") else None
            ),
        }

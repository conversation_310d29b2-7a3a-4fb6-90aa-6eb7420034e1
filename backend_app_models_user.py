from __future__ import annotations

"""User model helpers wrapping MongoDB documents.

This app is strictly *single‑tenant / single‑user*, but we keep a thin wrapper
around the ``users`` collection so future multi‑user expansion remains easy.
Everything is static‑method driven to avoid introducing a heavyweight ODM.
"""

from datetime import datetime
from typing import Any, Dict, Optional

from bson.objectid import ObjectId
from flask_login import UserMixin
from werkzeug.security import check_password_hash, generate_password_hash

from app.extensions import mongo


COLL = mongo.db.users  # typed alias


class User(UserMixin):
    """Lightweight adapter that plugs MongoDB docs into Flask‑Login."""

    def __init__(self, doc: Dict[str, Any]):
        self._doc = doc
        # Flask‑Login expects ``id`` attr (not ``_id``) and stringified
        self.id: str = str(doc["_id"])

    # ---------------------------------------------------------------------
    # Core lookup helpers (class/static methods)
    # ---------------------------------------------------------------------

    @staticmethod
    def _by_query(query: Dict[str, Any]) -> "User | None":
        if (doc := COLL.find_one(query)) is not None:
            return User(doc)
        return None

    @classmethod
    def get(cls, user_id: str | ObjectId) -> "User | None":
        """Find a user by ``_id`` (string or :class:`bson.ObjectId`)."""
        obj_id = ObjectId(user_id) if isinstance(user_id, str) else user_id
        return cls._by_query({"_id": obj_id})

    @classmethod
    def by_username(cls, username: str) -> "User | None":
        return cls._by_query({"username": username})

    # ---------------------------------------------------------------------
    # Creation & authentication
    # ---------------------------------------------------------------------

    @classmethod
    def create(cls, *, username: str, password: str, email: str | None = None, phone: str | None = None) -> "User":
        """Insert a new user and return its adapter instance."""

        now = datetime.utcnow()
        doc = {
            "username": username,
            "pw_hash": generate_password_hash(password),
            "email": email,
            "phone": phone,
            "dashboard_layout": {},
            "categories": [],
            "created_at": now,
            "updated_at": now,
        }
        COLL.insert_one(doc)
        return User(doc)

    # ---------------------------------------------------------------------
    # Instance helpers
    # ---------------------------------------------------------------------

    def verify_password(self, password: str) -> bool:
        return check_password_hash(self._doc["pw_hash"], password)

    def update_profile(self, *, username: Optional[str] = None, email: Optional[str] = None, phone: Optional[str] = None) -> None:
        """Partial update of profile fields (no password)."""
        update: Dict[str, Any] = {"updated_at": datetime.utcnow()}
        if username:
            update["username"] = username
        if email is not None:  # allow clearing by passing ""
            update["email"] = email or None
        if phone is not None:
            update["phone"] = phone or None
        COLL.update_one({"_id": ObjectId(self.id)}, {"$set": update})
        self._doc.update(update)

    def set_password(self, new_password: str) -> None:
        new_hash = generate_password_hash(new_password)
        COLL.update_one({"_id": ObjectId(self.id)}, {"$set": {"pw_hash": new_hash, "updated_at": datetime.utcnow()}})
        self._doc["pw_hash"] = new_hash

    # ---------------------------------------------------------------------
    # Flask‑Login required properties
    # ---------------------------------------------------------------------

    @property
    def is_authenticated(self) -> bool:  # pragma: no cover – inherited OK
        return True

    @property
    def is_active(self) -> bool:  # pragma: no cover
        return True

    @property
    def is_anonymous(self) -> bool:  # pragma: no cover
        return False

    def get_id(self) -> str:  # pragma: no cover
        return self.id

    # ---------------------------------------------------------------------
    # Serialisation helper (for API responses)
    # ---------------------------------------------------------------------

    def to_dict(self) -> Dict[str, Any]:
        """Return a safe serialisable representation (no password hash)."""
        return {
            "id": self.id,
            "username": self._doc["username"],
            "email": self._doc.get("email"),
            "phone": self._doc.get("phone"),
            "dashboard_layout": self._doc.get("dashboard_layout", {}),
            "categories": self._doc.get("categories", []),
        }

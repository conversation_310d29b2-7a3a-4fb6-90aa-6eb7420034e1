from __future__ import annotations

"""Contact‑Type CRUD blueprint.

Exposes endpoints for managing contact categories and their *threshold_days*
colour‑coding values.

Routes
------
GET    /api/contact-types            → list all types
POST   /api/contact-types            → create new type
PATCH  /api/contact-types/<id>       → update label/threshold
DELETE /api/contact-types/<id>       → delete type (cascade handled separately)
"""

from typing import Any, Dict, List

from bson import ObjectId
from flask import Blueprint, jsonify, request
from flask_login import login_required
from werkzeug.exceptions import BadRequest, NotFound

from app.models.contact_type import ContactType

bp = Blueprint("contact_types", __name__, url_prefix="/api/contact-types")


# ---------------------------------------------------------------------------
# Helpers
# ---------------------------------------------------------------------------


def _json_error(msg: str, code: int = 400):
    return jsonify({"error": msg}), code


def _payload(ct: ContactType):
    return ct.to_dict()


# ---------------------------------------------------------------------------
# Routes
# ---------------------------------------------------------------------------


@bp.get("/")
@login_required
def list_contact_types():
    types: List[ContactType] = ContactType.list_all()
    return jsonify({"contact_types": [_payload(t) for t in types]})


@bp.post("/")
@login_required
def create_contact_type():
    if not request.is_json:
        raise BadRequest("Request must be JSON")

    data: Dict[str, Any] = request.get_json(silent=True) or {}
    label = data.get("label", "").strip()
    threshold = data.get("threshold_days")

    if not label or threshold is None:
        return _json_error("label and threshold_days are required")

    if ContactType.by_label(label):
        return _json_error("Label already exists", 409)

    try:
        threshold_int = int(threshold)
        if threshold_int <= 0:
            raise ValueError
    except ValueError:
        return _json_error("threshold_days must be a positive integer", 422)

    ct = ContactType.create(label=label, threshold_days=threshold_int)
    return jsonify({"contact_type": _payload(ct)}), 201


@bp.patch("/<ct_id>")
@login_required
def update_contact_type(ct_id: str):
    ct = ContactType.get(ct_id)
    if ct is None:
        raise NotFound("Contact‑type not found")

    data = request.get_json(silent=True) or {}
    update: Dict[str, Any] = {}

    if "label" in data:
        label = data["label"].strip()
        if not label:
            return _json_error("label cannot be empty")
        if label != ct._doc["label"] and ContactType.by_label(label):
            return _json_error("Label already exists", 409)
        update["label"] = label

    if "threshold_days" in data:
        try:
            th = int(data["threshold_days"])
            if th <= 0:
                raise ValueError
        except ValueError:
            return _json_error("threshold_days must be a positive integer", 422)
        update["threshold_days"] = th

    ct.update(**update)
    return jsonify({"contact_type": _payload(ct)})


@bp.delete("/<ct_id>")
@login_required
def delete_contact_type(ct_id: str):
    ct = ContactType.get(ct_id)
    if ct is None:
        raise NotFound("Contact‑type not found")

    ct.delete()
    return {"status": "deleted"}, 204

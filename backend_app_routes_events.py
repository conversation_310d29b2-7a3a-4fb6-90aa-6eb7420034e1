from __future__ import annotations

"""Appointments & Activities CRUD blueprint.

Handles both one-off and recurring events (RRULE). All datetimes are stored
and returned in UTC ISO‑8601; the front‑end converts to the local timezone for
rendering.

Routes
------
GET    /api/events                 → list events with next instances
POST   /api/events                 → create event
PATCH  /api/events/<event_id>      → partial update
DELETE /api/events/<event_id>      → hard delete
"""

from typing import Any, Dict, List, Optional

from bson import ObjectId
from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from werkzeug.exceptions import BadRequest, NotFound

from app.models.event import Event
from app.utils.datetime import parse_iso8601

bp = Blueprint("events", __name__, url_prefix="/api/events")


# ---------------------------------------------------------------------------
# Helpers
# ---------------------------------------------------------------------------


def _json_error(msg: str, code: int = 400):
    return jsonify({"error": msg}), code


# ---------------------------------------------------------------------------
# Serialisation
# ---------------------------------------------------------------------------


def _event_payload(evt: Event):
    return evt.to_dict(include_instances=True)


# ---------------------------------------------------------------------------
# Validators
# ---------------------------------------------------------------------------


def _validate_start_end(start_raw: str, end_raw: str):
    try:
        start_dt = parse_iso8601(start_raw)
        end_dt = parse_iso8601(end_raw)
    except ValueError as exc:
        raise BadRequest(str(exc)) from exc
    if end_dt <= start_dt:
        raise BadRequest("end must be after start")
    return start_dt, end_dt


# ---------------------------------------------------------------------------
# Routes
# ---------------------------------------------------------------------------


@bp.get("/")
@login_required
def list_events():
    events: List[Event] = Event.list_by_user(current_user.id)
    return jsonify({"events": [_event_payload(e) for e in events]})


@bp.post("/")
@login_required
def create_event():
    if not request.is_json:
        raise BadRequest("Request must be JSON")
    data: Dict[str, Any] = request.get_json(silent=True) or {}

    title = data.get("title", "").strip()
    if not title:
        return _json_error("Title is required")

    start_raw = data.get("start")
    end_raw = data.get("end")
    if not (start_raw and end_raw):
        return _json_error("start and end are required")

    start_dt, end_dt = _validate_start_end(start_raw, end_raw)

    rrule = (data.get("rrule") or "").strip() or None
    notes = (data.get("notes") or "").strip() or None

    evt = Event.create(
        user_id=current_user.id,
        title=title,
        start=start_dt,
        end=end_dt,
        rrule=rrule,
        notes=notes,
    )
    return jsonify({"event": _event_payload(evt)}), 201


@bp.patch("/<event_id>")
@login_required
def update_event(event_id: str):
    evt = Event.get(event_id)
    if evt is None:
        raise NotFound("Event not found")
    if str(evt._doc["user_id"]) != current_user.id:
        return _json_error("Forbidden", 403)

    data = request.get_json(silent=True) or {}
    update: Dict[str, Any] = {}

    if "title" in data:
        ttl = data["title"].strip()
        if not ttl:
            return _json_error("Title cannot be empty")
        update["title"] = ttl

    if {"start", "end"} & data.keys():
        s_raw = data.get("start") or evt._doc["start"].isoformat()
        e_raw = data.get("end") or evt._doc["end"].isoformat()
        start_dt, end_dt = _validate_start_end(s_raw, e_raw)
        update["start"] = start_dt
        update["end"] = end_dt

    if "rrule" in data:
        update["rrule"] = (data["rrule"].strip() or None)

    if "notes" in data:
        update["notes"] = (data["notes"].strip() or None)

    if update:
        evt.update(**update)

    return jsonify({"event": _event_payload(evt)})


@bp.delete("/<event_id>")
@login_required
def delete_event(event_id: str):
    evt = Event.get(event_id)
    if evt is None:
        raise NotFound("Event not found")
    if str(evt._doc["user_id"]) != current_user.id:
        return _json_error("Forbidden", 403)

    evt.delete()
    return {"status": "deleted"}, 204

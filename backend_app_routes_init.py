"""Blueprint aggregator.

This module provides a single helper ``register_blueprints(app)`` that
collects and registers every feature‑specific blueprint with the Flask app.
Keeping the imports local to the function avoids circular‑import headaches
while the project is still evolving one file at a time.
"""

from flask import Flask

# ---------------------------------------------------------------------------
# Public API
# ---------------------------------------------------------------------------


def register_blueprints(app: Flask) -> None:
    """Attach all API blueprints to *app*.

    Import statements are placed *inside* this function so that individual
    blueprint modules can import shared utilities (e.g. ``current_app``) at
    module scope without triggering circular dependencies during the initial
    app factory execution.
    """

    # --- Auth ----------------------------------------------------------------
    from .auth import bp as auth_bp  # noqa: WPS433 (internal import by design)

    app.register_blueprint(auth_bp)

    # Future blueprints (bills, events, todos, etc.) will be added here as the
    # implementation phase proceeds, always respecting the one‑file delivery
    # contract.

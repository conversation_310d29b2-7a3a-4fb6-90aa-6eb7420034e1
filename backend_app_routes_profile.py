from __future__ import annotations

"""Profile management blueprint (single-user admin).

Endpoints
---------
GET    /api/profile            → current profile details
PATCH  /api/profile            → update username / email / phone
PATCH  /api/profile/password   → change password (confirm required)

All routes require authentication (single-user system).  Input validation is
kept minimal; the React front‑end performs additional client‑side checks.
"""

from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from werkzeug.exceptions import BadRequest

from app.models.user import User

bp = Blueprint("profile", __name__, url_prefix="/api/profile")


# ---------------------------------------------------------------------------
# Helpers
# ---------------------------------------------------------------------------


def _json_error(message: str, code: int = 400):
    return jsonify({"error": message}), code


# ---------------------------------------------------------------------------
# Routes
# ---------------------------------------------------------------------------


@bp.get("/")
@login_required
def get_profile():
    return jsonify({"user": current_user.to_dict()})


@bp.patch("/")
@login_required
def update_profile():
    if not request.is_json:
        raise BadRequest("Request must be JSON")

    data = request.get_json(silent=True) or {}
    username = data.get("username")
    email = data.get("email")
    phone = data.get("phone")

    # Basic validation
    if username is not None and not username.strip():
        return _json_error("Username cannot be empty", 422)

    # Duplicate username check (single‑user app, but guard anyway)
    if username and User.by_username(username) and username != current_user.username:
        return _json_error("Username already taken", 409)

    current_user.update_profile(username=username, email=email, phone=phone)
    return jsonify({"user": current_user.to_dict()})


@bp.patch("/password")
@login_required
def change_password():
    if not request.is_json:
        raise BadRequest("Request must be JSON")

    data = request.get_json(silent=True) or {}
    old_pw = data.get("old_password", "")
    new_pw = data.get("new_password", "")
    confirm = data.get("confirm", "")

    if not all((old_pw, new_pw, confirm)):
        return _json_error("All password fields are required", 422)

    if not current_user.verify_password(old_pw):
        return _json_error("Current password incorrect", 403)

    if new_pw != confirm:
        return _json_error("Password confirmation does not match", 422)

    current_user.set_password(new_pw)
    return {"status": "password_updated"}, 204

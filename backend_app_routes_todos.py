"""Blueprint aggregator.

Registers every API blueprint with the Flask app. Imports live within the
function to avoid circular dependencies during application startup.
"""

from flask import Flask


def register_blueprints(app: Flask) -> None:  # noqa: WPS231
    """Attach all API blueprints to *app*."""

    # --- Auth --------------------------------------------------------------
    from .auth import bp as auth_bp  # type: ignore
    app.register_blueprint(auth_bp)

    # --- Profile -----------------------------------------------------------
    from .profile import bp as profile_bp  # type: ignore
    app.register_blueprint(profile_bp)

    # --- Bills & Vacations -------------------------------------------------
    from .bills import bp as bills_bp  # type: ignore
    app.register_blueprint(bills_bp)

    # --- Events / Appointments --------------------------------------------
    from .events import bp as events_bp  # type: ignore
    app.register_blueprint(events_bp)

    # --- To‑Dos ------------------------------------------------------------
    from .todos import bp as todos_bp  # type: ignore
    app.register_blueprint(todos_bp)

    # Future blueprints (contacts, contact-types, dashboard, weather, etc.)
    # will be appended as their implementation files are delivered.

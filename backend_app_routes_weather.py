from __future__ import annotations

"""Weather proxy endpoint.

Retrieves current + 3‑day weather for a supported city code using the cached
service wrapper.  Requires authentication so that only the legitimate user
affects the weather API rate budget.

Route
-----
GET /api/weather?city=<code>    → raw Open‑Meteo payload
"""

from flask import Blueprint, jsonify, request
from flask_login import login_required
from werkzeug.exceptions import BadRequest

from app.services.weather import get_weather

bp = Blueprint("weather", __name__, url_prefix="/api/weather")


@bp.get("/")
@login_required
def weather():
    city = (request.args.get("city") or "stl").lower()
    try:
        payload = get_weather(city)
    except ValueError as exc:
        raise BadRequest(str(exc)) from exc

    return jsonify({"city": city, "weather": payload})

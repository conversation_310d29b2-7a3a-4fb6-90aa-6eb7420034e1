from __future__ import annotations

"""Open‑Meteo weather proxy / cache service.

This module wraps all interaction with the Open‑Meteo API and stores a small
cache in the ``weather_cache`` collection to stay well under the free‑tier
limits (≤ 12 calls / city / day).  The cache TTL is configurable via
``app.config['WEATHER_CACHE_TTL']`` and defaults to 7 200 s (2 h).

Usage
-----
>>> from app.services.weather import get_weather
>>> payload = get_weather("stl")  # St Louis, MO

The returned *payload* is the raw JSON dict from Open‑Meteo.  The front‑end
is responsible for rendering current conditions and a 3‑day outlook.
"""

from datetime import datetime, timezone
from typing import Any, Dict

import requests
from flask import current_app

from app.extensions import mongo

COLL = mongo.db.weather_cache

# ---------------------------------------------------------------------------
# City → coordinates mapping (extend as needed)
# ---------------------------------------------------------------------------

_CITIES: Dict[str, Dict[str, float]] = {
    "stl": {"lat": 38.6270, "lon": -90.1994},  # St Louis, Missouri, USA
    "acc": {"lat": 5.6037, "lon": -0.1870},   # Accra, Ghana
}


# ---------------------------------------------------------------------------
# Public helper
# ---------------------------------------------------------------------------


def get_weather(city_code: str) -> Dict[str, Any]:
    """Return current + 3‑day weather payload for *city_code*.

    Results are cached in MongoDB for ``WEATHER_CACHE_TTL`` seconds to minimise
    external API calls.  If an error occurs during fetch, the stale cache (if
    any) is returned to keep the dashboard functional.
    """

    city_code = city_code.lower()
    if city_code not in _CITIES:
        raise ValueError(f"Unsupported city code: {city_code}")

    cfg_ttl: int = int(current_app.config.get("WEATHER_CACHE_TTL", 7200))
    now = datetime.utcnow().replace(tzinfo=timezone.utc)

    # Check cached doc
    if (doc := COLL.find_one({"_id": city_code})) is not None:
        age = (now - doc["fetched_at"]).total_seconds()
        if age < cfg_ttl:
            return doc["payload"]  # fresh enough

    # Build request
    coords = _CITIES[city_code]
    params = {
        "latitude": coords["lat"],
        "longitude": coords["lon"],
        "temperature_unit": "fahrenheit",
        "forecast_days": 3,
        "timezone": "auto",
        "current": "temperature_2m",
        "daily": "temperature_2m_max,temperature_2m_min,weather_code",
    }

    try:
        resp = requests.get("https://api.open-meteo.com/v1/forecast", params=params, timeout=5)
        resp.raise_for_status()
        payload: Dict[str, Any] = resp.json()
    except Exception as exc:  # noqa: WPS424 (broad – network errors etc.)
        current_app.logger.warning("Weather fetch failed for %s: %s", city_code, exc)
        if doc is not None:  # return stale cache
            return doc["payload"]
        raise

    # Upsert cache
    COLL.update_one(
        {"_id": city_code},
        {"$set": {"fetched_at": now, "payload": payload}},
        upsert=True,
    )
    return payload

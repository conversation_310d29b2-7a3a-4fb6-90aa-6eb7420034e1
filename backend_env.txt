# -------------------------------------------------------------------------
# Environment variables for the Personal Organizer backend (example)
# Copy to `.env` and adjust values before running in production.
# -------------------------------------------------------------------------

# Flask runtime environment: `production` or `development`
FLASK_ENV=production

# Cryptographic secret for sessions & CSRF tokens – **change this**
SECRET_KEY=replace-with-64-char-random-string

# -------------------------------------------------------------------------
# MongoDB
# -------------------------------------------------------------------------
# Connection string format: mongodb://<user>:<pass>@host:port/dbname
MONGO_URI=mongodb://localhost:27017/personal_organizer

# -------------------------------------------------------------------------
# Security / CORS
# -------------------------------------------------------------------------
# Comma‑separated list of allowed front‑end origins
CORS_ALLOWED_ORIGINS=http://localhost:5173,http://127.0.0.1:5173

# Rate‑limiting pattern (Flask‑Limiter syntax). 5/min is the default.
RATELIMIT_DEFAULT=5 per minute

# -------------------------------------------------------------------------
# Weather integration
# -------------------------------------------------------------------------
# Cache TTL in seconds (default 7200 = 2 hours)
WEATHER_CACHE_TTL=7200

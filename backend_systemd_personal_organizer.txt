[Unit]
Description=Personal Organizer Flask API via Gunicorn
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/opt/personal-organizer/backend
# Ensure Gunicorn sees env variables; adjust path as needed
EnvironmentFile=/opt/personal-organizer/backend/.env
ExecStart=/usr/bin/gunicorn -c gunicorn_config.py wsgi:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=5
LimitNOFILE=4096

[Install]
WantedBy=multi-user.target

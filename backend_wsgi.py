"""Gunicorn entry point.

Example command:
    gunicorn -c backend/gunicorn_config.py backend.wsgi:app

The application instance is created lazily via the factory pattern to ensure
that configuration and extensions are fully initialised regardless of the
runtime context (Gun<PERSON> worker, Flask CLI, or `python backend/wsgi.py`).
"""

from app import create_app

# Lazily create the Flask application
app = create_app()


if __name__ == "__main__":  # pragma: no cover
    # Enable hot‑reload when executed directly (useful for quick tests)
    app.run(host="127.0.0.1", port=5000, debug=True)

#!/usr/bin/env python3
"""Check if the test user exists and verify password."""

import sys
import os
sys.path.append('backend')

from pymongo import MongoClient
from werkzeug.security import check_password_hash

def check_user():
    # Connect to MongoDB
    client = MongoClient('mongodb://localhost:27017/')
    db = client.personal_organizer
    users_collection = db.users
    
    # Find the user
    user = users_collection.find_one({"username": "admin"})
    if not user:
        print("User 'admin' not found!")
        return
    
    print(f"User found: {user['username']}")
    print(f"Email: {user.get('email', 'None')}")
    print(f"Created: {user.get('created_at', 'Unknown')}")
    
    # Test password verification
    password = "password123"
    pw_hash = user['pw_hash']
    
    print(f"Password hash: {pw_hash[:50]}...")
    
    is_valid = check_password_hash(pw_hash, password)
    print(f"Password verification result: {is_valid}")
    
    if not is_valid:
        print("Password verification failed! Let's try recreating the user...")
        # Delete and recreate
        users_collection.delete_one({"username": "admin"})
        
        from werkzeug.security import generate_password_hash
        from datetime import datetime
        
        now = datetime.utcnow()
        new_user_doc = {
            "username": "admin",
            "pw_hash": generate_password_hash("password123"),
            "email": "<EMAIL>",
            "phone": None,
            "dashboard_layout": {},
            "categories": [],
            "created_at": now,
            "updated_at": now,
        }
        
        result = users_collection.insert_one(new_user_doc)
        print(f"Recreated user with ID: {result.inserted_id}")
        
        # Verify the new password
        new_is_valid = check_password_hash(new_user_doc['pw_hash'], password)
        print(f"New password verification result: {new_is_valid}")

if __name__ == "__main__":
    check_user()

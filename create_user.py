#!/usr/bin/env python3
"""Create a test user for the Personal Organizer application."""

import sys
import os
sys.path.append('backend')

from pymongo import MongoClient
from werkzeug.security import generate_password_hash
from datetime import datetime

def create_test_user():
    # Connect to MongoDB
    client = MongoClient('mongodb://localhost:27017/')
    db = client.personal_organizer
    users_collection = db.users
    
    # Check if user already exists
    existing_user = users_collection.find_one({"username": "admin"})
    if existing_user:
        print("User 'admin' already exists!")
        return
    
    # Create test user
    now = datetime.utcnow()
    user_doc = {
        "username": "admin",
        "pw_hash": generate_password_hash("password123"),
        "email": "<EMAIL>",
        "phone": None,
        "dashboard_layout": {},
        "categories": [],
        "created_at": now,
        "updated_at": now,
    }
    
    result = users_collection.insert_one(user_doc)
    print(f"Created user 'admin' with ID: {result.inserted_id}")
    print("Login credentials:")
    print("  Username: admin")
    print("  Password: password123")

if __name__ == "__main__":
    create_test_user()

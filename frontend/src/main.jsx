import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "./index.css"; // Tailwind base + components + utilities

import App from "@/App";
import { AuthProvider } from "@/context/AuthContext";

// ---------------------------------------------------------------------------
// Initialisation
// ---------------------------------------------------------------------------

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 min default
      retry: 1,
    },
  },
});

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </AuthProvider>
      {import.meta.env.MODE === "development" && <ReactQueryDevtools initialIsOpen={false} />}
      <ToastContainer position="top-right" autoClose={3000} hideProgressBar newestOnTop />
    </QueryClientProvider>
  </React.StrictMode>
);

import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { loadEnv } from "vite";
import path from "path";
import { fileURLToPath, URL } from "node:url";

// ---------------------------------------------------------------------------
// Vite configuration
// ---------------------------------------------------------------------------

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  return {
    plugins: [react()],

    server: {
      port: 5173,
      proxy: {
        // Proxy all /api requests to the Flask backend running on localhost:8001
        "/api": {
          target: env.VITE_BACKEND_URL || "http://127.0.0.1:8001",
          changeOrigin: true,
          secure: false,
        },
      },
    },

    build: {
      outDir: "dist",
      sourcemap: mode === "development",
    },

    // Enable absolute imports with @/** syntax
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },

    css: {
      postcss: {
        plugins: [
          // autoprefixer is included via postcss.config.js
        ],
      },
    },
  };
});

{"name": "personal-organizer-frontend", "version": "1.0.0", "private": true, "description": "World‑class personal organizer — React + Vite front‑end", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 4173"}, "dependencies": {"@tanstack/react-query": "^5.36.0", "axios": "^1.7.2", "clsx": "^2.1.0", "date-fns": "^3.6.0", "lucide-react": "^0.324.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-grid-layout": "^1.4.4", "react-router-dom": "^6.23.0", "react-window": "^1.8.9"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.17", "postcss": "^8.4.33", "tailwindcss": "^3.4.4", "vite": "^5.0.8"}}
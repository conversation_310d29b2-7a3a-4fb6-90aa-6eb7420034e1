import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { CalendarClock } from "lucide-react";
import { format } from "date-fns";

function BillRow({ bill }) {
  const dueDate = new Date(bill.due);
  const isPast = dueDate < new Date();

  return (
    <div
      className={clsx(
        "flex items-center justify-between py-1 px-2 rounded",
        isPast ? "opacity-50" : ""
      )}
    >
      <div className="flex items-center gap-2 overflow-hidden">
        <CalendarClock size={16} className="shrink-0 text-brand-500" />
        <span className="truncate text-sm font-medium">{bill.title}</span>
      </div>
      <div className="flex items-center gap-3 text-sm">
        {bill.amount && <span className="font-semibold">${bill.amount}</span>}
        <span className="text-gray-500">
          {format(dueDate, "MMM d")}
        </span>
      </div>
    </div>
  );
}

BillRow.propTypes = {
  bill: PropTypes.shape({
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    due: PropTypes.string.isRequired,
    amount: PropTypes.string,
  }).isRequired,
};

export default function BillsCard({ items }) {
  return (
    <div className="flex h-full flex-col">
      <h3 className="mb-2 text-lg font-semibold">Upcoming Bills</h3>
      <div className="flex-1 space-y-1 overflow-y-auto pr-1">
        {items.length === 0 && (
          <p className="text-sm text-gray-500">No bills in next 30 days.</p>
        )}
        {items.map((bill) => (
          <BillRow key={bill.id + bill.due} bill={bill} />
        ))}
      </div>
    </div>
  );
}

BillsCard.propTypes = {
  items: PropTypes.arrayOf(PropTypes.object).isRequired,
};

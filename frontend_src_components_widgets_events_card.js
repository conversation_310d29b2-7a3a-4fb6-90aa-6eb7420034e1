import React from "react";
import PropTypes from "prop-types";
import { CalendarRange } from "lucide-react";
import { format } from "date-fns";
import clsx from "clsx";

function EventRow({ evt }) {
  const start = new Date(evt.start);
  const sameDay = start.toDateString() === new Date(evt.end).toDateString();

  return (
    <div className="flex items-center justify-between rounded py-1 px-2 hover:bg-gray-50 dark:hover:bg-gray-700">
      <div className="flex items-center gap-2 overflow-hidden">
        <CalendarRange size={16} className="shrink-0 text-brand-500" />
        <span className="truncate text-sm font-medium">{evt.title}</span>
      </div>
      <span
        className={clsx(
          "text-xs font-semibold",
          sameDay ? "text-gray-500" : "text-brand-600 dark:text-brand-400"
        )}
      >
        {format(start, sameDay ? "MMM d, h:mma" : "MMM d")}
      </span>
    </div>
  );
}

EventRow.propTypes = {
  evt: PropTypes.shape({
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    start: PropTypes.string.isRequired,
    end: PropTypes.string.isRequired,
  }).isRequired,
};

export default function EventsCard({ items }) {
  return (
    <div className="flex h-full flex-col">
      <h3 className="mb-2 text-lg font-semibold">Upcoming Events</h3>
      <div className="flex-1 space-y-1 overflow-y-auto pr-1">
        {items.length === 0 && (
          <p className="text-sm text-gray-500">No events in next 30 days.</p>
        )}
        {items.map((evt) => (
          <EventRow key={evt.id + evt.start} evt={evt} />
        ))}
      </div>
    </div>
  );
}

EventsCard.propTypes = {
  items: PropTypes.arrayOf(PropTypes.object).isRequired,
};

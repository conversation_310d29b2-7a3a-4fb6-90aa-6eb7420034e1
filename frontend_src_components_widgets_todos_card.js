import React, { useState } from "react";
import PropTypes from "prop-types";
import axios from "axios";
import { CheckCircle, Circle } from "lucide-react";
import Spinner from "@/components/Spinner";
import { useQueryClient } from "@tanstack/react-query";
import clsx from "clsx";

export default function TodosCard({ items }) {
  const [adding, setAdding] = useState(false);
  const [text, setText] = useState("");
  const queryClient = useQueryClient();

  async function toggleComplete(todo) {
    await axios.patch(`/api/todos/${todo.id}`, { completed: !todo.completed_at });
    queryClient.invalidateQueries(["dashboard"]);
  }

  async function handleAdd(e) {
    e.preventDefault();
    if (!text.trim()) return;
    setAdding(true);
    try {
      await axios.post("/api/todos", { text });
      setText("");
      queryClient.invalidateQueries(["dashboard"]);
    } finally {
      setAdding(false);
    }
  }

  return (
    <div className="flex h-full flex-col">
      <h3 className="mb-2 text-lg font-semibold">To‑Dos</h3>

      <form onSubmit={handleAdd} className="mb-2 flex gap-2">
        <input
          type="text"
          placeholder="Add new…"
          className="flex-1 rounded-lg border border-gray-300 dark:border-gray-700 bg-transparent px-2 py-1 text-sm"
          value={text}
          onChange={(e) => setText(e.target.value)}
        />
        <button
          type="submit"
          disabled={adding}
          className="rounded-lg bg-brand-500 px-3 text-sm font-medium text-white hover:bg-brand-600 disabled:opacity-50"
        >
          {adding ? <Spinner size={16} /> : "Add"}
        </button>
      </form>

      <div className="flex-1 space-y-1 overflow-y-auto pr-1">
        {items.length === 0 && <p className="text-sm text-gray-500">All caught up!</p>}
        {items.map((todo) => (
          <div
            key={todo.id}
            className="flex items-center gap-2 rounded py-1 px-2 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <button
              onClick={() => toggleComplete(todo)}
              className="shrink-0 text-brand-500"
              aria-label="Toggle complete"
            >
              {todo.completed_at ? <CheckCircle size={18} /> : <Circle size={18} />}
            </button>
            <span
              className={clsx(
                "truncate text-sm",
                todo.completed_at && "line-through text-gray-500"
              )}
            >
              {todo.text}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}

TodosCard.propTypes = {
  items: PropTypes.arrayOf(PropTypes.object).isRequired,
};

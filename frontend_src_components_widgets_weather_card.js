import React from "react";
import PropTypes from "prop-types";
import { RefreshCw } from "lucide-react";
import clsx from "clsx";

function formatTemp(t) {
  return `${Math.round(t)}°F`;
}

export default function WeatherCard({ data, onRefresh }) {
  if (!data) {
    return <p className="text-sm text-gray-500">No weather data.</p>;
  }

  const current = data?.current;
  const daily = data?.daily;

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-lg font-semibold">Weather</h3>
        <button
          onClick={onRefresh}
          className="rounded-full p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
          aria-label="Refresh weather"
        >
          <RefreshCw size={18} className="text-gray-500" />
        </button>
      </div>

      {/* Current conditions */}
      <div className="flex items-baseline gap-2 mb-4">
        <span className="text-4xl font-bold leading-none">
          {formatTemp(current?.temperature_2m)}
        </span>
        <span className="text-sm text-gray-500">{data?.timezone_abbreviation}</span>
      </div>

      {/* 3‑day outlook */}
      <div className="grid grid-cols-3 gap-2 mt-auto">
        {daily?.time?.map((dateStr, idx) => (
          <div
            key={dateStr}
            className={clsx(
              "rounded-lg p-2 text-center",
              idx === 0 ? "bg-brand-50 dark:bg-gray-700" : "bg-gray-50 dark:bg-gray-800"
            )}
          >
            <p className="text-xs mb-1 font-medium">
              {new Date(dateStr).toLocaleDateString(undefined, {
                weekday: "short",
              })}
            </p>
            <p className="text-sm font-semibold">
              {formatTemp(daily.temperature_2m_max[idx])}
            </p>
            <p className="text-xs text-gray-500">
              {formatTemp(daily.temperature_2m_min[idx])}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}

WeatherCard.propTypes = {
  data: PropTypes.object.isRequired,
  onRefresh: PropTypes.func.isRequired,
};

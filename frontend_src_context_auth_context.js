import React, { createContext, useContext, useEffect, useState } from "react";
import axios from "axios";
import PropTypes from "prop-types";

// ---------------------------------------------------------------------------
// Context setup
// ---------------------------------------------------------------------------

const AuthContext = createContext({
  isAuthenticated: false,
  user: null,
  loading: true,
  login: async () => {},
  logout: async () => {},
});

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch session on initial mount
  useEffect(() => {
    async function fetchSession() {
      try {
        const { data } = await axios.get("/api/auth/me");
        setUser(data.user);
      } catch {
        // not logged in or session expired
        setUser(null);
      } finally {
        setLoading(false);
      }
    }
    fetchSession();
  }, []);

  // -----------------------------------------------------------------------
  // Actions
  // -----------------------------------------------------------------------

  async function login(username, password) {
    const { data } = await axios.post("/api/auth/login", { username, password });
    setUser(data.user);
  }

  async function logout() {
    await axios.post("/api/auth/logout");
    setUser(null);
  }

  const value = {
    isAuthenticated: Boolean(user),
    user,
    loading,
    login,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

AuthProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export function useAuth() {
  return useContext(AuthContext);
}

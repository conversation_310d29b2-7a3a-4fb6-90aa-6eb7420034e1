import React from "react";
import axios from "axios";
import { Responsive, Width<PERSON><PERSON>ider } from "react-grid-layout";
import { useQuery } from "@tanstack/react-query";
import Spinner from "@/components/Spinner";
import WeatherCard from "@/components/widgets/WeatherCard";
import BillsCard from "@/components/widgets/BillsCard";
import EventsCard from "@/components/widgets/EventsCard";
import TodosCard from "@/components/widgets/TodosCard";
import ContactsCard from "@/components/widgets/ContactsCard";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";

const ResponsiveGridLayout = WidthProvider(Responsive);

// Default layout if user has none saved yet (will be persisted later)
const defaultLayout = [
  { i: "weather", x: 0, y: 0, w: 2, h: 3 },
  { i: "bills", x: 2, y: 0, w: 2, h: 4 },
  { i: "events", x: 0, y: 3, w: 2, h: 4 },
  { i: "todos", x: 2, y: 4, w: 2, h: 5 },
  { i: "contacts", x: 0, y: 7, w: 4, h: 3 },
];

export default function DashboardPage() {
  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["dashboard"],
    queryFn: async () => {
      const { data } = await axios.get("/api/dashboard");
      return data;
    },
    staleTime: 1000 * 60 * 2, // 2 min – ok, weather TTL is 2h but other data may change
  });

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Spinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p className="text-red-500">Failed to load dashboard.</p>
      </div>
    );
  }

  const { weather, bills, events, todos, contacts } = data;

  return (
    <div className="p-4">
      <ResponsiveGridLayout
        className="layout"
        layouts={{ lg: defaultLayout }}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 4, md: 4, sm: 2, xs: 2, xxs: 1 }}
        rowHeight={30}
        isResizable={true}
        isDraggable={true}
        compactType="vertical"
      >
        <div key="weather" className="rounded-xl bg-white dark:bg-gray-800 shadow p-2 overflow-hidden">
          <WeatherCard data={weather} onRefresh={refetch} />
        </div>
        <div key="bills" className="rounded-xl bg-white dark:bg-gray-800 shadow p-2 overflow-hidden">
          <BillsCard items={bills} />
        </div>
        <div key="events" className="rounded-xl bg-white dark:bg-gray-800 shadow p-2 overflow-hidden">
          <EventsCard items={events} />
        </div>
        <div key="todos" className="rounded-xl bg-white dark:bg-gray-800 shadow p-2 overflow-hidden">
          <TodosCard items={todos} />
        </div>
        <div key="contacts" className="rounded-xl bg-white dark:bg-gray-800 shadow p-2 overflow-hidden">
          <ContactsCard items={contacts} />
        </div>
      </ResponsiveGridLayout>
    </div>
  );
}

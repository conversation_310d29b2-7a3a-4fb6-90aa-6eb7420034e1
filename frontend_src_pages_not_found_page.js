import React from "react";
import { <PERSON> } from "react-router-dom";
import { AlertTriangle } from "lucide-react";

export default function NotFoundPage() {
  return (
    <div className="flex h-screen flex-col items-center justify-center gap-6 dark:bg-gray-900 bg-brand-50 px-4">
      <AlertTriangle size={48} className="text-brand-500" />
      <h1 className="text-3xl font-semibold text-gray-900 dark:text-gray-100">Page Not Found</h1>
      <p className="text-center text-gray-500 max-w-sm">
        The page you are looking for doesn&apos;t exist or has been moved.
      </p>
      <Link
        to="/"
        className="rounded-lg bg-brand-500 px-4 py-2 text-white hover:bg-brand-600"
      >
        Go to Dashboard
      </Link>
    </div>
  );
}

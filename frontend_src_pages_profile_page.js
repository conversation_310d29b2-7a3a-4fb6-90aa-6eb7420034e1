import React, { useState } from "react";
import axios from "axios";
import { useAuth } from "@/context/AuthContext";
import Spinner from "@/components/Spinner";
import { toast } from "react-toastify";

export default function ProfilePage() {
  const { user, setUser } = useAuth();

  const [info, setInfo] = useState({
    username: user?.username || "",
    email: user?.email || "",
    phone: user?.phone || "",
  });

  const [passwords, setPasswords] = useState({ old: "", new: "", confirm: "" });
  const [saving, setSaving] = useState(false);
  const [pwSaving, setPwSaving] = useState(false);

  async function handleInfoSave(e) {
    e.preventDefault();
    setSaving(true);
    try {
      const { data } = await axios.patch("/api/profile", info);
      setUser(data.user);
      toast.success("Profile updated");
    } catch (err) {
      toast.error(err.response?.data?.error || "Update failed");
    } finally {
      setSaving(false);
    }
  }

  async function handlePwSave(e) {
    e.preventDefault();
    if (passwords.new !== passwords.confirm) {
      toast.error("New password and confirmation do not match");
      return;
    }
    setPwSaving(true);
    try {
      await axios.patch("/api/profile/password", {
        old_password: passwords.old,
        new_password: passwords.new,
        confirm: passwords.confirm,
      });
      toast.success("Password updated");
      setPasswords({ old: "", new: "", confirm: "" });
    } catch (err) {
      toast.error(err.response?.data?.error || "Password update failed");
    } finally {
      setPwSaving(false);
    }
  }

  if (!user) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Spinner />
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-2xl p-4">
      <h2 className="mb-6 text-2xl font-semibold">Profile</h2>

      {/* Profile info */}
      <form onSubmit={handleInfoSave} className="mb-10 space-y-4">
        <div>
          <label htmlFor="username" className="block text-sm font-medium">
            Username
          </label>
          <input
            id="username"
            name="username"
            type="text"
            required
            className="mt-1 block w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-transparent px-3 py-2"
            value={info.username}
            onChange={(e) => setInfo({ ...info, username: e.target.value })}
          />
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium">
            Email (optional)
          </label>
          <input
            id="email"
            name="email"
            type="email"
            className="mt-1 block w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-transparent px-3 py-2"
            value={info.email}
            onChange={(e) => setInfo({ ...info, email: e.target.value })}
          />
        </div>

        <div>
          <label htmlFor="phone" className="block text-sm font-medium">
            Phone (optional)
          </label>
          <input
            id="phone"
            name="phone"
            type="tel"
            className="mt-1 block w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-transparent px-3 py-2"
            value={info.phone}
            onChange={(e) => setInfo({ ...info, phone: e.target.value })}
          />
        </div>

        <button
          type="submit"
          disabled={saving}
          className="flex items-center rounded-lg bg-brand-500 px-4 py-2 text-white hover:bg-brand-600 disabled:opacity-50"
        >
          {saving ? <Spinner size={20} /> : "Save Changes"}
        </button>
      </form>

      {/* Password change */}
      <h3 className="mb-4 text-xl font-semibold">Change Password</h3>
      <form onSubmit={handlePwSave} className="space-y-4">
        <div>
          <label htmlFor="old_pw" className="block text-sm font-medium">
            Current Password
          </label>
          <input
            id="old_pw"
            name="old"
            type="password"
            required
            className="mt-1 block w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-transparent px-3 py-2"
            value={passwords.old}
            onChange={(e) => setPasswords({ ...passwords, old: e.target.value })}
          />
        </div>
        <div>
          <label htmlFor="new_pw" className="block text-sm font-medium">
            New Password
          </label>
          <input
            id="new_pw"
            name="new"
            type="password"
            required
            className="mt-1 block w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-transparent px-3 py-2"
            value={passwords.new}
            onChange={(e) => setPasswords({ ...passwords, new: e.target.value })}
          />
        </div>
        <div>
          <label htmlFor="confirm_pw" className="block text-sm font-medium">
            Confirm New Password
          </label>
          <input
            id="confirm_pw"
            name="confirm"
            type="password"
            required
            className="mt-1 block w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-transparent px-3 py-2"
            value={passwords.confirm}
            onChange={(e) => setPasswords({ ...passwords, confirm: e.target.value })}
          />
        </div>

        <button
          type="submit"
          disabled={pwSaving}
          className="flex items-center rounded-lg bg-brand-500 px-4 py-2 text-white hover:bg-brand-600 disabled:opacity-50"
        >
          {pwSaving ? <Spinner size={20} /> : "Update Password"}
        </button>
      </form>
    </div>
  );
}

# Product Context - Personal Organizer

## Why This Project Exists
Personal organization tools are often fragmented across multiple apps, cloud-dependent, or lack the polish of commercial products. This project creates a unified, self-hosted solution that matches SaaS quality while maintaining complete user control.

## Problems It Solves
- **Fragmentation**: Eliminates need for multiple apps (calendar, todo, contacts, weather)
- **Privacy**: Self-hosted solution keeps personal data under user control
- **Customization**: Drag-and-drop dashboard allows personalized layouts
- **Relationship management**: Contact threshold system prevents relationship neglect
- **Recurring tasks**: Proper RFC 5545 RRULE support for complex recurring patterns
- **Weather context**: Integrated weather helps with daily planning

## How It Should Work
### User Experience Flow
1. **Simple login**: Username/password with rate limiting protection
2. **Dashboard overview**: All widgets visible at a glance with drag-and-drop customization
3. **Quick actions**: Add todos, bills, events directly from dashboard widgets
4. **Detail management**: Click through to full CRUD interfaces for each feature
5. **Relationship tracking**: Visual indicators for contacts needing attention
6. **Weather awareness**: Current conditions and forecast integrated into planning

### Key Interactions
- **Dashboard**: Primary interface with customizable widget layout
- **Forms**: Clean, responsive forms for all data entry
- **Lists**: Sortable, filterable lists for managing items
- **Calendar integration**: Visual representation of events and bill due dates
- **Contact pinging**: Easy way to log contact interactions

## User Experience Goals
- **Immediate value**: Dashboard shows everything important at a glance
- **Effortless entry**: Quick add functionality for common tasks
- **Visual clarity**: Color coding and clear typography throughout
- **Mobile responsive**: Works well on all device sizes
- **Fast performance**: Sub-second response times for all interactions
- **Reliable**: No data loss, proper error handling, graceful degradation

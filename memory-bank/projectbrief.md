# Personal Organizer - Project Brief

## Core Mission
Build a world-class, single-user personal organizer that consolidates bills, vacations, appointments, contacts, to-dos and weather inside an elegant drag-and-drop dashboard. Self-hosted SaaS-quality application for Ubuntu homelabs.

## Key Requirements
- **Single-user focus**: Minimal login with username + password
- **Dashboard-centric**: React-Grid-Layout with draggable widgets
- **Comprehensive features**: Bills, events, todos, contacts, weather
- **Recurring logic**: RFC 5545 RRULEs for bills and events
- **Contact management**: Color-coded with threshold days for relationship maintenance
- **Weather integration**: Open-Meteo proxy with MongoDB caching
- **Production-ready**: Gunicorn + systemd deployment, Cloudflare Tunnel support

## Technical Stack
- **Backend**: Python 3, Flask 3, Flask-<PERSON>gin, Flask-PyMongo
- **Database**: MongoDB 6 Community with TTL indexes
- **Frontend**: React 18, Vite 5, Tailwind 3, React-Query 5
- **Auth/Security**: <PERSON>lask-<PERSON><PERSON>, <PERSON><PERSON>k-Limiter (5 tries/min)
- **Deployment**: Gunicorn 22 + systemd, Cloudflare Tunnel ready

## Success Criteria
- Clean, responsive UI with dark mode support
- All CRUD operations working for each feature
- Recurring events and bills functioning correctly
- Weather data cached and displaying properly
- Contact threshold system operational
- Production deployment configuration complete
- Comprehensive testing with Playwright

## Project Status
- Phase 2 implementation complete
- All files reorganized according to schema
- Ready for dependency installation and testing

# System Patterns - Personal Organizer

## Architecture Overview
Clean separation between backend API and frontend SPA with MongoDB as the single data store.

```
Frontend (React/Vite) ←→ Backend (Flask API) ←→ MongoDB
                                ↓
                         External APIs (Weather)
```

## Key Technical Decisions

### Backend Patterns
- **Flask Application Factory**: Modular app creation in `app/__init__.py`
- **Blueprint Organization**: Each feature gets its own route blueprint
- **Model Layer**: MongoDB document models with validation
- **Service Layer**: Business logic separated from routes (weather service)
- **Utility Layer**: Shared helpers (datetime utilities)

### Frontend Patterns
- **Component Hierarchy**: Pages → Widgets → Components
- **Context Providers**: AuthContext for global authentication state
- **Widget Architecture**: Self-contained dashboard widgets
- **Page Routing**: React Router for SPA navigation
- **State Management**: React Query for server state, Context for global state

### Data Patterns
- **MongoDB Collections**: Users, bills, events, todos, contacts, contact_types, weather_cache
- **TTL Indexes**: Automatic cleanup for completed todos and weather cache
- **Compound Indexes**: Optimized queries for user-scoped data
- **UTC Storage**: All timestamps stored in UTC, displayed in America/Chicago

### Security Patterns
- **Session-based Auth**: Flask-Login with secure session cookies
- **Rate Limiting**: Flask-Limiter prevents brute force attacks
- **CORS Configuration**: Controlled cross-origin access
- **Input Validation**: Server-side validation for all inputs

## Component Relationships

### Backend Structure
```
app/
├── __init__.py          # App factory, blueprint registration
├── config.py            # Environment-based configuration
├── extensions.py        # Flask extension initialization
├── models/              # MongoDB document models
├── routes/              # API endpoints grouped by feature
├── services/            # Business logic and external integrations
└── utils/               # Shared utility functions
```

### Frontend Structure
```
src/
├── main.jsx             # App entry point
├── App.jsx              # Root component with routing
├── context/             # Global state providers
├── components/          # Reusable UI components
│   └── widgets/         # Dashboard widget components
└── pages/               # Route-level page components
```

## Design Patterns in Use
- **Factory Pattern**: Flask app factory
- **Repository Pattern**: Model classes abstract MongoDB operations
- **Observer Pattern**: React state updates trigger re-renders
- **Facade Pattern**: Service layer simplifies complex operations
- **Strategy Pattern**: Different authentication strategies possible

# Technical Context - Personal Organizer

## Technology Stack

### Backend Technologies
- **Python 3**: Core language
- **Flask 3**: Web framework with application factory pattern
- **Flask-Login**: Session-based authentication
- **Flask-PyMongo**: MongoDB integration
- **Flask-Limiter**: Rate limiting protection
- **Flask-CORS**: Cross-origin resource sharing
- **python-decouple**: Environment variable management
- **dateutil**: RFC 5545 RRULE parsing and date manipulation
- **Gunicorn**: WSGI server for production

### Frontend Technologies
- **React 18**: UI library with hooks and functional components
- **Vite 5**: Build tool and dev server
- **Tailwind CSS 3**: Utility-first CSS framework
- **React Query 5**: Server state management
- **React Router**: Client-side routing
- **Lucide React**: Icon library
- **React Grid Layout**: Drag-and-drop dashboard widgets

### Database
- **MongoDB 6 Community**: Document database
- **TTL Indexes**: Automatic data cleanup
- **Compound Indexes**: Query optimization
- **Collections**: users, bills, events, todos, contacts, contact_types, weather_cache

### Development Setup
- **Node.js**: Frontend build tools and package management
- **npm**: Package manager for frontend dependencies
- **Python venv**: Backend virtual environment
- **pip**: Python package management

### Production Deployment
- **systemd**: Service management
- **Gunicorn**: Production WSGI server
- **Cloudflare Tunnel**: HTTPS and external access
- **Ubuntu 22.04**: Target deployment platform

## Technical Constraints
- **Single-user**: No multi-tenancy complexity
- **Self-hosted**: No cloud dependencies except weather API
- **MongoDB required**: Application designed around document storage
- **UTC timestamps**: All dates stored in UTC, displayed in America/Chicago
- **Session-based auth**: No JWT or token-based authentication

## Dependencies

### Backend (requirements.txt)
- Flask and extensions for web framework
- PyMongo for database connectivity
- python-decouple for configuration
- dateutil for date/time handling
- Gunicorn for production serving

### Frontend (package.json)
- React ecosystem packages
- Vite for build tooling
- Tailwind for styling
- React Query for data fetching
- Various utility libraries

## Development Workflow
1. **Backend**: Flask development server on port 8000
2. **Frontend**: Vite dev server on port 5173 with proxy to backend
3. **Database**: Local MongoDB instance
4. **Testing**: Playwright for end-to-end testing

## Production Configuration
- **Environment files**: .env for backend, frontend env vars
- **Process management**: systemd service unit
- **Reverse proxy**: Cloudflare Tunnel or Nginx
- **Static files**: Frontend build served separately

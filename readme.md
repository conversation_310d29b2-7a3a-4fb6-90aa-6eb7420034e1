# Personal Organizer – Self‑Hosted SaaS‑Quality App

A world‑class, single‑user organizer that consolidates **bills, vacations, appointments, contacts, to‑dos and weather** inside an elegant drag‑and‑drop dashboard.  Built for Ubuntu homelabs yet polished like a commercial product.

---

## ✨ Features

- **Minimal login** – username + password secured by Flask‑Login & Flask‑Limiter.
- **Dashboard** – React‑Grid‑Layout with widgets for weather, bills, events, to‑dos, contacts.
- **Recurring logic** – bills & events powered by RFC 5545 RRULEs (dateutil).
- **Colour‑coded contacts** – keep relationships warm via threshold days.
- **Weather** – Open‑Meteo proxy cached 2 h in MongoDB.
- **Responsive UI** – React 18, Vite, TailwindCSS dark‑mode.
- **Homelab‑friendly** – Gunicorn + systemd, Cloudflare Tunnel HTTPS, Mongo TTL cleanup.

---

## ⚙️  Tech Stack

| Layer           | Tech                                             |
| --------------- | ------------------------------------------------ |
| Backend         | Python 3 · Flask 3 · Flask‑Login · Flask‑PyMongo |
| Database        | MongoDB 6 Community + TTL Indexes                |
| Front‑end       | React 18 · Vite 5 · Tailwind 3 · React‑Query 5   |
| Auth / Rate‑lim | Flask‑Login · Flask‑Limiter (5 tries/min)        |
| Deployment      | Gunicorn 22 + systemd unit · Cloudflare Tunnel   |
| Other libs      | python‑decouple, dateutil, lucide‑react icons    |

---

## 🏗️  Local Setup (Ubuntu 22.04)

```bash
# 1 — clone repo
$ git clone https://github.com/youruser/personal-organizer.git && cd personal-organizer

# 2 — backend virtualenv
$ python3 -m venv venv && source venv/bin/activate
$ pip install -r backend/requirements.txt
$ cp backend/.env.example backend/.env
#   → edit SECRET_KEY & MONGO_URI if needed

# 3 — frontend deps
$ cd frontend
$ npm install        # (uses package.json + lockfile)
$ cp .env.example .env  # adjust VITE_BACKEND_URL if backend not localhost

# 4 — start dev servers (two tabs)
$ npm run dev        # front‑end on http://localhost:5173
$ cd ../backend && flask --app app run -p 8000
```

### MongoDB

```bash
# Ubuntu repo install
$ sudo apt install -y mongodb-org
$ sudo systemctl enable --now mongod
```

The default `MONGO_URI` uses `personal_organizer` database and requires no auth when running locally.

---

## 🚀  Production Deployment (systemd + Gunicorn + Cloudflare Tunnel)

1. **Copy project** to `/opt/personal-organizer` owned by `www-data`.
2. Populate `/opt/personal-organizer/backend/.env` with production values.
3. **Enable unit**:
   ```bash
   $ sudo ln -s /opt/personal-organizer/backend/systemd/personal-organizer.service \
        /etc/systemd/system/
   $ sudo systemctl daemon-reload && sudo systemctl enable --now personal-organizer
   ```
4. **Cloudflare Tunnel**:
   ```bash
   $ cloudflared tunnel create organizer
   $ cloudflared tunnel route dns organizer organizer.example.com
   $ cloudflared tunnel run organizer --url http://127.0.0.1:8000
   ```
5. **Frontend build**:
   ```bash
   $ cd /opt/personal-organizer/frontend
   $ npm run build
   # serve dist/ via Nginx or Cloudflare Pages; adjust CORS OR frontend env
   ```

---

## 🔑  Environment Variables

| File & Variable          | Purpose                                      |
| ------------------------ | -------------------------------------------- |
| **backend/.env**         |                                              |
|   `FLASK_ENV`            | `production` or `development`                |
|   `SECRET_KEY`           | 64‑char random string for sessions + CSRF    |
|   `MONGO_URI`            | MongoDB connection URI                       |
|   `CORS_ALLOWED_ORIGINS` | Comma list of allowed front‑end origins      |
|   `WEATHER_CACHE_TTL`    | Seconds to cache Open‑Meteo responses (7200) |
| **frontend/.env**        |                                              |
|   `VITE_BACKEND_URL`     | Base URL of Flask API used by Vite dev proxy |

---

## 📑  API Overview

> All endpoints are prefixed with `/api` and require the session cookie after `/auth/login`.

| Verb & Path                            | Description                    |
| -------------------------------------- | ------------------------------ |
| `POST /auth/login`                     | Log in, returns `{user}`       |
| `POST /auth/logout`                    | Clear session                  |
| `GET /dashboard`                       | Aggregated dashboard payload   |
| `GET/POST/PATCH/DELETE /bills`         | Bills & vacations CRUD         |
| `GET/POST/PATCH/DELETE /events`        | Appointments CRUD              |
| `GET/POST/PATCH/DELETE /todos`         | To‑Dos CRUD                    |
| `GET/POST/PATCH/DELETE /contacts`      | Contacts CRUD + `/ping`        |
| `GET/POST/PATCH/DELETE /contact-types` | Manage contact type thresholds |
| `GET /weather?city=stl`                | Cached Open‑Meteo proxy        |

Full JSON shapes are documented inline in each route file.

---

## 🧪  Running Tests (optional)

```bash
$ pytest
```

---

## 📜  License

MIT © 2025 Your Name


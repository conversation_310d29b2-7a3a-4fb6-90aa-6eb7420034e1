# World‑Class Personal Organizer – Phase 1

## 1  MongoDB Database Schema (logical model)

| Collection | Key Fields & Types | Notes / Indexes |
| ---------- | ------------------ | --------------- |
|            |                    |                 |

| **users**          | `_id` (ObjectId) • `username` (string, unique) • `pw_hash` (string) • `email?` (string) • `phone?` (string) • `dashboard_layout` (object) • `categories` (array)                                                       | 🔹 `username` unique index🔹 `email` sparse unique index |
| ------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------- |
| **contact\_types** | `_id` (ObjectId) • `label` (string, unique) • `threshold_days` (int)                                                                                                                                                   | 🔹 `label` unique index                                  |
| **contacts**       | `_id` • `user_id` (ref users) • `type_id` (ref contact\_types) • `name` (string) • `last_contact` (datetime UTC)                                                                                                       | 🔹 `user_id` idx🔹 `type_id` idx                         |
| **bills**          | `_id` • `user_id` • `title` (string) • `due_date?` (datetime UTC) • `rrule?` (string RFC 5545) • `amount?` (Decimal128) • `category?` (string) • `notes?` (string) • `created_at` (datetime) • `updated_at` (datetime) | 🔹 compound `{user_id, due_date}` idx                    |
| **events**         | `_id` • `user_id` • `title` • `start` (datetime UTC) • `end` (datetime UTC) • `rrule?` (string) • `notes?` (string) • `created_at`                                                                                     | 🔹 compound `{user_id, start}` idx                       |
| **todos**          | `_id` • `user_id` • `text` (string) • `created_at` (datetime) • `completed_at?` (datetime)                                                                                                                             | 🔹 `user_id` idx🔹 TTL index on `completed_at` (+24 h)   |
| **weather\_cache** | `_id` (string city code) • `fetched_at` (datetime) • `payload` (object raw)                                                                                                                                            | 🔹 TTL index on `fetched_at` (+2 h)                      |

*All timestamps stored in UTC; the front‑end renders them in ****America/Chicago****.*

---

## 2  Project Directory Structure (top ‑ level)

```
personal-organizer/
├── backend/
│   ├── app/
│   │   ├── __init__.py          # Flask app factory
│   │   ├── config.py            # settings via python‑decouple
│   │   ├── extensions.py        # db, login_manager, limiter, cors
│   │   ├── models/              # Mongo models / helpers
│   │   ├── routes/              # Blueprints per resource
│   │   ├── services/            # business logic & integrations
│   │   └── utils/               # shared helpers
│   ├── wsgi.py                  # Gunicorn entry‑point
│   ├── requirements.txt
│   ├── gunicorn_config.py
│   ├── systemd/
│   │   └── personal-organizer.service
│   └── .env.example
├── frontend/
│   ├── src/
│   │   ├── assets/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── api/                 # React‑Query wrappers
│   │   ├── context/             # global providers (auth, theme)
│   │   ├── routes/
│   │   └── main.jsx             # Vite entry
│   ├── index.html
│   ├── vite.config.js
│   ├── tailwind.config.js
│   ├── postcss.config.js
│   ├── package.json
│   └── .env.example             # frontend env vars
├── README.md
└── LICENSE
```



